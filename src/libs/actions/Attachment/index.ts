import Onyx, {OnyxCollection} from 'react-native-onyx';
import {FileObject} from '@components/AttachmentModal';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Attachment} from '@src/types/onyx';

let attachments: OnyxCollection<Attachment> | undefined;
Onyx.connect({
    key: ONYXKEYS.COLLECTION.ATTACHMENT,
    waitForCollectionCallback: true,
    callback: (value) => (attachments = value),
});

function uploadAttachment(attachmentID: string, file: FileObject) {}

function getAttachmentSource(attachmentID: string) {
    return '';
}

function cacheAttachment(attachmentID: string, uri: string, fileType: string) {}

export {uploadAttachment, getAttachmentSource, cacheAttachment};
