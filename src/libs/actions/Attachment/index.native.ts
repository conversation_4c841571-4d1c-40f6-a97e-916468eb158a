import RNFetchBlob from 'react-native-blob-util';
import Onyx, {OnyxCollection} from 'react-native-onyx';
import {FileObject} from '@components/AttachmentModal';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Attachment} from '@src/types/onyx';

let attachments: OnyxCollection<Attachment> | undefined;
Onyx.connect({
    key: ONYXKEYS.COLLECTION.ATTACHMENT,
    waitForCollectionCallback: true,
    callback: (value) => (attachments = value),
});

function uploadAttachment(attachmentID: string, file: FileObject) {
    if (!attachmentID || !file.uri) {
        return;
    }

    Onyx.set(`${ONYXKEYS.COLLECTION.ATTACHMENT}${attachmentID}`, {
        attachmentID,
        source: file.uri,
    });
}

function getAttachmentSource(attachmentID: string) {
    if (!attachmentID) {
        return;
    }
    const attachment = attachments?.[attachmentID];

    return attachment?.source;
}

function cacheAttachment(attachmentID: string, uri: string, fileType: string) {
    // if (!attachmentID || !uri || !fileType) {
    //     return;
    // }
    RNFetchBlob.config({fileCache: true, appendExt: fileType})
        .fetch('GET', uri)
        .then(async (response) => {
            let result = await RNFetchBlob.MediaCollection.copyToMediaStore(
                {
                    name: `${attachmentID}.${fileType}`,
                    parentFolder: '',
                    mime: 'image/jpeg',
                },
                'Image',
                response.path(),
            );
            response.flush();
            console.log('result', result);
        })
        .catch((error) => {
            console.log('error', error);
        });
}

export {uploadAttachment, getAttachmentSource, cacheAttachment};
